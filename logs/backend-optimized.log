/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework_simplejwt/__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
Performing system checks...

System check identified some issues:

WARNINGS:
?: (guardian.W001) Guardian authentication backend is not hooked. You can add this in settings as eg: `AUTHENTICATION_BACKENDS = ('django.contrib.auth.backends.ModelBackend', 'guardian.backends.ObjectPermissionBackend')`.
?: (staticfiles.W004) The directory '/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/static' in the STATICFILES_DIRS setting does not exist.
?: (urls.W005) URL namespace 'authentication' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'clients' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'projects' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'tasks' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'team' isn't unique. You may not be able to reverse all URLs in this namespace

System check identified 7 issues (0 silenced).
June 05, 2025 - 03:58:36
Django version 4.2.9, using settings 'mtbrmg_erp.settings'
Starting development server at http://127.0.0.1:8000/
Quit the server with CONTROL-C.

"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/tasks/ HTTP/1.1" 200 1277
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"OPTIONS /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 0
"OPTIONS /api/clients/?page_size=100 HTTP/1.1" 200 0
"OPTIONS /api/dashboard/overview/ HTTP/1.1" 200 0
"OPTIONS /api/team/?page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"OPTIONS /api/commissions/stats/ HTTP/1.1" 200 0
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
- Broken pipe from ('127.0.0.1', 56560)
- Broken pipe from ('127.0.0.1', 56561)
- Broken pipe from ('127.0.0.1', 56562)
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
- Broken pipe from ('127.0.0.1', 56559)
"GET /api/commissions/stats/ HTTP/1.1" 200 362
- Broken pipe from ('127.0.0.1', 56674)
"GET /api/team/stats/ HTTP/1.1" 200 327
- Broken pipe from ('127.0.0.1', 56675)
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
- Broken pipe from ('127.0.0.1', 56934)
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
- Broken pipe from ('127.0.0.1', 56935)
"GET /api/team/stats/ HTTP/1.1" 200 327
- Broken pipe from ('127.0.0.1', 56936)
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
- Broken pipe from ('127.0.0.1', 57172)
- Broken pipe from ('127.0.0.1', 57173)
- Broken pipe from ('127.0.0.1', 57175)
- Broken pipe from ('127.0.0.1', 57174)
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
- Broken pipe from ('127.0.0.1', 57170)
- Broken pipe from ('127.0.0.1', 57171)
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
- Broken pipe from ('127.0.0.1', 57371)
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
- Broken pipe from ('127.0.0.1', 57370)
- Broken pipe from ('127.0.0.1', 57372)
- Broken pipe from ('127.0.0.1', 57369)
- Broken pipe from ('127.0.0.1', 57373)
- Broken pipe from ('127.0.0.1', 57368)
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Not Found: /api/quotations/
Not Found: /api/quotations/stats/
"GET /api/quotations/ HTTP/1.1" 404 43357
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
- Broken pipe from ('127.0.0.1', 57605)
- Broken pipe from ('127.0.0.1', 57606)
Not Found: /api/quotations/
Not Found: /api/quotations/stats/
"GET /api/quotations/ HTTP/1.1" 404 43357
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
- Broken pipe from ('127.0.0.1', 57611)
- Broken pipe from ('127.0.0.1', 57612)
Not Found: /api/quotations/
"GET /api/quotations/ HTTP/1.1" 404 43357
- Broken pipe from ('127.0.0.1', 57614)
Not Found: /api/quotations/stats/
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
- Broken pipe from ('127.0.0.1', 57616)
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/dashboard/overview/ HTTP/1.1" 200 0
"OPTIONS /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 0
"OPTIONS /api/clients/?page_size=100 HTTP/1.1" 200 0
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
"OPTIONS /api/team/?page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 0
"OPTIONS /api/clients/?page_size=100 HTTP/1.1" 200 0
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
"OPTIONS /api/dashboard/overview/ HTTP/1.1" 200 0
"OPTIONS /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/team/?page_size=10 HTTP/1.1" 200 0
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
Not Found: /api/quotations/stats/
Not Found: /api/quotations/
"GET /api/quotations/ HTTP/1.1" 404 43357
- Broken pipe from ('127.0.0.1', 57717)
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
- Broken pipe from ('127.0.0.1', 57718)
Not Found: /api/quotations/
Not Found: /api/quotations/stats/
"GET /api/quotations/ HTTP/1.1" 404 43357
- Broken pipe from ('127.0.0.1', 57721)
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
- Broken pipe from ('127.0.0.1', 57722)
Not Found: /api/quotations/
Not Found: /api/quotations/stats/
"GET /api/quotations/ HTTP/1.1" 404 43357
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
- Broken pipe from ('127.0.0.1', 57724)
- Broken pipe from ('127.0.0.1', 57726)
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"OPTIONS /api/quotations/stats/ HTTP/1.1" 200 0
"OPTIONS /api/quotations/ HTTP/1.1" 200 0
"OPTIONS /api/quotations/stats/ HTTP/1.1" 200 0
"OPTIONS /api/quotations/ HTTP/1.1" 200 0
Not Found: /api/quotations/
"GET /api/quotations/ HTTP/1.1" 404 43357
Not Found: /api/quotations/stats/
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
Not Found: /api/quotations/
Not Found: /api/quotations/stats/
"GET /api/quotations/ HTTP/1.1" 404 43357
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
"OPTIONS /api/service-catalog/?is_active=true HTTP/1.1" 200 0
"OPTIONS /api/service-catalog/?is_active=true HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
Not Found: /api/service-catalog/
"GET /api/service-catalog/?is_active=true HTTP/1.1" 404 43387
Not Found: /api/service-catalog/
"GET /api/service-catalog/?is_active=true HTTP/1.1" 404 43387
"GET /api/clients/ HTTP/1.1" 200 2183
"GET /api/clients/ HTTP/1.1" 200 2183
Not Found: /api/quotations/stats/
Not Found: /api/quotations/
"GET /api/quotations/ HTTP/1.1" 404 43357
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
Not Found: /api/quotations/
Not Found: /api/quotations/stats/
"GET /api/quotations/ HTTP/1.1" 404 43357
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Not Found: /api/quotations/stats/
Not Found: /api/quotations/
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
"GET /api/quotations/ HTTP/1.1" 404 43357
Not Found: /api/quotations/
"GET /api/quotations/ HTTP/1.1" 404 43357
Not Found: /api/quotations/stats/
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Not Found: /api/quotations/
"GET /api/quotations/ HTTP/1.1" 404 43357
Not Found: /api/quotations/stats/
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
Not Found: /api/quotations/
"GET /api/quotations/ HTTP/1.1" 404 43357
Not Found: /api/quotations/stats/
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/quotations/ HTTP/1.1" 200 0
"OPTIONS /api/quotations/stats/ HTTP/1.1" 200 0
"OPTIONS /api/quotations/ HTTP/1.1" 200 0
"OPTIONS /api/quotations/stats/ HTTP/1.1" 200 0
Not Found: /api/quotations/
Not Found: /api/quotations/
"GET /api/quotations/ HTTP/1.1" 404 43357
Not Found: /api/quotations/stats/
Not Found: /api/quotations/stats/
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
"GET /api/quotations/ HTTP/1.1" 404 43357
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
Bad Request: /api/tasks/
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"OPTIONS /api/commissions/stats/ HTTP/1.1" 200 0
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
