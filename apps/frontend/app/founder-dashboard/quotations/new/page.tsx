'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { quotationsAPI, serviceCatalogAPI, type CreateQuotationData, type ServiceCatalog } from '@/lib/api/quotations';
import { ArrowRight, Plus, Save, Trash2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface QuotationItem {
  service_id: number;
  service?: ServiceCatalog;
  description: string;
  quantity: number;
  unit_price: number;
  complexity_level: string;
  estimated_hours: number;
  total_price: number;
}

const NewQuotationPage = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [services, setServices] = useState<ServiceCatalog[]>([]);
  const [clients, setClients] = useState<any[]>([]);
  
  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    client_id: '',
    priority: 'medium',
    discount_percentage: 0,
    tax_percentage: 14,
    valid_until: '',
    notes: '',
    terms_conditions: ''
  });

  const [items, setItems] = useState<QuotationItem[]>([]);

  useEffect(() => {
    fetchServices();
    fetchClients();
    
    // Set default validity date (30 days from now)
    const defaultDate = new Date();
    defaultDate.setDate(defaultDate.getDate() + 30);
    setFormData(prev => ({
      ...prev,
      valid_until: defaultDate.toISOString().split('T')[0]
    }));
  }, []);

  const fetchServices = async () => {
    try {
      const data = await serviceCatalogAPI.getServices({ is_active: true });
      setServices(data.results || data);
    } catch (error) {
      console.error('Error fetching services:', error);
      toast.error('حدث خطأ في تحميل الخدمات');
    }
  };

  const fetchClients = async () => {
    try {
      // Import clients API
      const { clientsAPI } = await import('@/lib/api');
      const data = await clientsAPI.getClients();
      setClients(data.results || data);
    } catch (error) {
      console.error('Error fetching clients:', error);
      toast.error('حدث خطأ في تحميل العملاء');
    }
  };

  const addItem = () => {
    const newItem: QuotationItem = {
      service_id: 0,
      description: '',
      quantity: 1,
      unit_price: 0,
      complexity_level: 'medium',
      estimated_hours: 8,
      total_price: 0
    };
    setItems([...items, newItem]);
  };

  const removeItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const updateItem = (index: number, field: keyof QuotationItem, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    
    // Auto-calculate total price
    if (field === 'quantity' || field === 'unit_price') {
      updatedItems[index].total_price = updatedItems[index].quantity * updatedItems[index].unit_price;
    }
    
    // Auto-fill from service
    if (field === 'service_id') {
      const service = services.find(s => s.id === value);
      if (service) {
        updatedItems[index].service = service;
        updatedItems[index].description = service.description_ar;
        updatedItems[index].unit_price = parseFloat(service.base_price);
        updatedItems[index].estimated_hours = service.estimated_hours_min;
        updatedItems[index].total_price = updatedItems[index].quantity * parseFloat(service.base_price);
      }
    }
    
    setItems(updatedItems);
  };

  const calculateTotals = () => {
    const subtotal = items.reduce((sum, item) => sum + item.total_price, 0);
    const discountAmount = subtotal * (formData.discount_percentage / 100);
    const afterDiscount = subtotal - discountAmount;
    const taxAmount = afterDiscount * (formData.tax_percentage / 100);
    const total = afterDiscount + taxAmount;

    return {
      subtotal,
      discountAmount,
      taxAmount,
      total
    };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.client_id || items.length === 0) {
      toast.error('يرجى ملء جميع الحقول المطلوبة وإضافة عنصر واحد على الأقل');
      return;
    }

    setLoading(true);
    
    try {
      const quotationData: CreateQuotationData = {
        ...formData,
        client_id: parseInt(formData.client_id),
        items_data: items.map(item => ({
          service: item.service_id,
          description: item.description,
          quantity: item.quantity,
          unit_price: item.unit_price,
          complexity_level: item.complexity_level,
          estimated_hours: item.estimated_hours
        }))
      };

      const result = await quotationsAPI.createQuotation(quotationData);
      toast.success('تم إنشاء العرض بنجاح');
      router.push(`/founder-dashboard/quotations/${result.id}`);
    } catch (error: any) {
      console.error('Error creating quotation:', error);
      toast.error(error.response?.data?.message || 'حدث خطأ في إنشاء العرض');
    } finally {
      setLoading(false);
    }
  };

  const totals = calculateTotals();

  return (
    <div className="container mx-auto p-6 space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowRight className="w-4 h-4" />
          رجوع
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">إنشاء عرض سعر جديد</h1>
          <p className="text-gray-600 mt-1">إنشاء عرض سعر احترافي للعملاء</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>المعلومات الأساسية</CardTitle>
            <CardDescription>معلومات العرض الأساسية</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="title">عنوان العرض *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="مثال: تطوير موقع إلكتروني"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="client">العميل *</Label>
                <Select value={formData.client_id} onValueChange={(value) => setFormData(prev => ({ ...prev, client_id: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر العميل" />
                  </SelectTrigger>
                  <SelectContent>
                    {clients.map((client) => (
                      <SelectItem key={client.id} value={client.id.toString()}>
                        {client.name} - {client.company || 'فردي'}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="description">وصف العرض</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="وصف تفصيلي للعرض..."
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="priority">الأولوية</Label>
                <Select value={formData.priority} onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">منخفض</SelectItem>
                    <SelectItem value="medium">متوسط</SelectItem>
                    <SelectItem value="high">عالي</SelectItem>
                    <SelectItem value="urgent">عاجل</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="valid_until">صالح حتى</Label>
                <Input
                  id="valid_until"
                  type="date"
                  value={formData.valid_until}
                  onChange={(e) => setFormData(prev => ({ ...prev, valid_until: e.target.value }))}
                />
              </div>

              <div>
                <Label htmlFor="tax_percentage">نسبة الضريبة (%)</Label>
                <Input
                  id="tax_percentage"
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  value={formData.tax_percentage}
                  onChange={(e) => setFormData(prev => ({ ...prev, tax_percentage: parseFloat(e.target.value) || 0 }))}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Items */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>عناصر العرض</CardTitle>
                <CardDescription>الخدمات والمنتجات المتضمنة في العرض</CardDescription>
              </div>
              <Button type="button" onClick={addItem} className="flex items-center gap-2">
                <Plus className="w-4 h-4" />
                إضافة عنصر
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {items.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                لا توجد عناصر. اضغط "إضافة عنصر" لبدء إنشاء العرض.
              </div>
            ) : (
              <div className="space-y-4">
                {items.map((item, index) => (
                  <div key={index} className="border rounded-lg p-4 space-y-4">
                    <div className="flex justify-between items-center">
                      <h4 className="font-medium">عنصر {index + 1}</h4>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeItem(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div>
                        <Label>الخدمة</Label>
                        <Select 
                          value={item.service_id.toString()} 
                          onValueChange={(value) => updateItem(index, 'service_id', parseInt(value))}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="اختر الخدمة" />
                          </SelectTrigger>
                          <SelectContent>
                            {services.map((service) => (
                              <SelectItem key={service.id} value={service.id.toString()}>
                                {service.name_ar}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label>الكمية</Label>
                        <Input
                          type="number"
                          min="1"
                          value={item.quantity}
                          onChange={(e) => updateItem(index, 'quantity', parseInt(e.target.value) || 1)}
                        />
                      </div>

                      <div>
                        <Label>سعر الوحدة (ج.م)</Label>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          value={item.unit_price}
                          onChange={(e) => updateItem(index, 'unit_price', parseFloat(e.target.value) || 0)}
                        />
                      </div>
                    </div>

                    <div>
                      <Label>الوصف</Label>
                      <Textarea
                        value={item.description}
                        onChange={(e) => updateItem(index, 'description', e.target.value)}
                        placeholder="وصف الخدمة..."
                        rows={2}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label>مستوى التعقيد</Label>
                        <Select 
                          value={item.complexity_level} 
                          onValueChange={(value) => updateItem(index, 'complexity_level', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="light">بسيط</SelectItem>
                            <SelectItem value="medium">متوسط</SelectItem>
                            <SelectItem value="complex">معقد</SelectItem>
                            <SelectItem value="enterprise">مؤسسي</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label>الساعات المقدرة</Label>
                        <Input
                          type="number"
                          min="1"
                          value={item.estimated_hours}
                          onChange={(e) => updateItem(index, 'estimated_hours', parseInt(e.target.value) || 1)}
                        />
                      </div>
                    </div>

                    <div className="text-left">
                      <span className="text-lg font-bold">
                        الإجمالي: {item.total_price.toLocaleString('ar-EG')} ج.م
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Totals */}
        {items.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>ملخص العرض</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>المجموع الفرعي:</span>
                  <span>{totals.subtotal.toLocaleString('ar-EG')} ج.م</span>
                </div>
                
                {formData.discount_percentage > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>الخصم ({formData.discount_percentage}%):</span>
                    <span>-{totals.discountAmount.toLocaleString('ar-EG')} ج.م</span>
                  </div>
                )}
                
                <div className="flex justify-between">
                  <span>الضريبة ({formData.tax_percentage}%):</span>
                  <span>{totals.taxAmount.toLocaleString('ar-EG')} ج.م</span>
                </div>
                
                <hr />
                
                <div className="flex justify-between text-lg font-bold">
                  <span>الإجمالي النهائي:</span>
                  <span>{totals.total.toLocaleString('ar-EG')} ج.م</span>
                </div>
              </div>

              <div className="mt-4">
                <Label htmlFor="discount_percentage">نسبة الخصم (%)</Label>
                <Input
                  id="discount_percentage"
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  value={formData.discount_percentage}
                  onChange={(e) => setFormData(prev => ({ ...prev, discount_percentage: parseFloat(e.target.value) || 0 }))}
                />
              </div>
            </CardContent>
          </Card>
        )}

        {/* Additional Information */}
        <Card>
          <CardHeader>
            <CardTitle>معلومات إضافية</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="notes">ملاحظات</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="ملاحظات إضافية..."
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="terms_conditions">الشروط والأحكام</Label>
              <Textarea
                id="terms_conditions"
                value={formData.terms_conditions}
                onChange={(e) => setFormData(prev => ({ ...prev, terms_conditions: e.target.value }))}
                placeholder="الشروط والأحكام..."
                rows={4}
              />
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={loading}
          >
            إلغاء
          </Button>
          <Button type="submit" disabled={loading} className="flex items-center gap-2">
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Save className="w-4 h-4" />
            )}
            حفظ العرض
          </Button>
        </div>
      </form>
    </div>
  );
};

export default NewQuotationPage;
