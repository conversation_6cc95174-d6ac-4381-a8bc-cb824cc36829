'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ArrowRight, CheckCircle, XCircle, FileText, Edit, Trash2, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { quotationsAPI, type Quotation } from '@/lib/api/quotations';
import { toast } from 'sonner';

const QuotationDetailPage = () => {
  const params = useParams();
  const router = useRouter();
  const [quotation, setQuotation] = useState<Quotation | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (params.id) {
      fetchQuotation();
    }
  }, [params.id]);

  const fetchQuotation = async () => {
    try {
      setLoading(true);
      const data = await quotationsAPI.getQuotation(Number(params.id));
      setQuotation(data);
    } catch (error) {
      console.error('Error fetching quotation:', error);
      toast.error('حدث خطأ في تحميل العرض');
      router.push('/founder-dashboard/quotations');
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async () => {
    if (!quotation) return;
    
    try {
      await quotationsAPI.approveQuotation(quotation.id, 'approve');
      toast.success('تم الموافقة على العرض بنجاح');
      fetchQuotation();
    } catch (error) {
      console.error('Error approving quotation:', error);
      toast.error('حدث خطأ في الموافقة على العرض');
    }
  };

  const handleReject = async () => {
    if (!quotation) return;
    
    try {
      await quotationsAPI.approveQuotation(quotation.id, 'reject');
      toast.success('تم رفض العرض');
      fetchQuotation();
    } catch (error) {
      console.error('Error rejecting quotation:', error);
      toast.error('حدث خطأ في رفض العرض');
    }
  };

  const handleConvertToProject = async () => {
    if (!quotation) return;
    
    try {
      const result = await quotationsAPI.convertToProject(quotation.id);
      toast.success('تم تحويل العرض إلى مشروع بنجاح');
      router.push(`/founder-dashboard/projects/${result.project_id}`);
    } catch (error) {
      console.error('Error converting to project:', error);
      toast.error('حدث خطأ في تحويل العرض إلى مشروع');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { label: 'مسودة', variant: 'secondary' as const },
      pending: { label: 'في الانتظار', variant: 'default' as const },
      approved: { label: 'موافق عليه', variant: 'default' as const },
      rejected: { label: 'مرفوض', variant: 'destructive' as const },
      expired: { label: 'منتهي الصلاحية', variant: 'secondary' as const },
      converted: { label: 'تم تحويله', variant: 'default' as const }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;

    return (
      <Badge variant={config.variant}>
        {config.label}
      </Badge>
    );
  };

  const formatCurrency = (amount: string) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(parseFloat(amount));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG');
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6" dir="rtl">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  if (!quotation) {
    return (
      <div className="container mx-auto p-6" dir="rtl">
        <div className="text-center py-8">
          <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">العرض غير موجود</h3>
          <p className="text-gray-600 mb-4">لم يتم العثور على العرض المطلوب</p>
          <Button onClick={() => router.push('/founder-dashboard/quotations')}>
            العودة إلى العروض
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowRight className="w-4 h-4" />
            رجوع
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{quotation.title}</h1>
            <p className="text-gray-600 mt-1">رقم العرض: {quotation.quotation_number}</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {getStatusBadge(quotation.status)}
          {quotation.is_expired && (
            <Badge variant="destructive">منتهي الصلاحية</Badge>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2 flex-wrap">
        {quotation.status === 'pending' && !quotation.is_expired && (
          <>
            <Button onClick={handleApprove} className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4" />
              موافقة
            </Button>
            <Button variant="outline" onClick={handleReject} className="flex items-center gap-2">
              <XCircle className="w-4 h-4" />
              رفض
            </Button>
          </>
        )}
        
        {quotation.status === 'approved' && !quotation.converted_to_project && (
          <Button onClick={handleConvertToProject} className="flex items-center gap-2">
            <FileText className="w-4 h-4" />
            تحويل إلى مشروع
          </Button>
        )}

        <Button variant="outline" className="flex items-center gap-2">
          <Download className="w-4 h-4" />
          تحميل PDF
        </Button>

        {quotation.status === 'draft' && (
          <Button variant="outline" className="flex items-center gap-2">
            <Edit className="w-4 h-4" />
            تعديل
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Client Information */}
          <Card>
            <CardHeader>
              <CardTitle>معلومات العميل</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="font-medium">الاسم:</span>
                  <span>{quotation.client.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">البريد الإلكتروني:</span>
                  <span>{quotation.client.email}</span>
                </div>
                {quotation.client.company && (
                  <div className="flex justify-between">
                    <span className="font-medium">الشركة:</span>
                    <span>{quotation.client.company}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Items */}
          <Card>
            <CardHeader>
              <CardTitle>عناصر العرض</CardTitle>
              <CardDescription>{quotation.items.length} عنصر</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {quotation.items.map((item, index) => (
                  <div key={item.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <h4 className="font-medium">{item.service.name_ar}</h4>
                        <p className="text-sm text-gray-600 mt-1">{item.description}</p>
                      </div>
                      <div className="text-left">
                        <div className="font-bold">{formatCurrency(item.total_price)}</div>
                        <div className="text-sm text-gray-600">
                          {item.quantity} × {formatCurrency(item.unit_price)}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center text-sm text-gray-600">
                      <div className="flex gap-4">
                        <span>التعقيد: {item.complexity_level_display}</span>
                        <span>الساعات المقدرة: {item.estimated_hours}</span>
                      </div>
                      <Badge variant="outline">{item.service.category_display}</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Description and Notes */}
          {(quotation.description || quotation.notes) && (
            <Card>
              <CardHeader>
                <CardTitle>تفاصيل إضافية</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {quotation.description && (
                  <div>
                    <h4 className="font-medium mb-2">وصف العرض</h4>
                    <p className="text-gray-700">{quotation.description}</p>
                  </div>
                )}
                
                {quotation.notes && (
                  <div>
                    <h4 className="font-medium mb-2">ملاحظات</h4>
                    <p className="text-gray-700">{quotation.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Terms and Conditions */}
          {quotation.terms_conditions && (
            <Card>
              <CardHeader>
                <CardTitle>الشروط والأحكام</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 whitespace-pre-line">{quotation.terms_conditions}</p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Summary */}
          <Card>
            <CardHeader>
              <CardTitle>ملخص العرض</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span>المجموع الفرعي:</span>
                <span>{formatCurrency(quotation.subtotal)}</span>
              </div>
              
              {parseFloat(quotation.discount_amount) > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>الخصم ({quotation.discount_percentage}%):</span>
                  <span>-{formatCurrency(quotation.discount_amount)}</span>
                </div>
              )}
              
              <div className="flex justify-between">
                <span>الضريبة ({quotation.tax_percentage}%):</span>
                <span>{formatCurrency(quotation.tax_amount)}</span>
              </div>
              
              <Separator />
              
              <div className="flex justify-between text-lg font-bold">
                <span>الإجمالي:</span>
                <span>{formatCurrency(quotation.total_amount)}</span>
              </div>
            </CardContent>
          </Card>

          {/* Details */}
          <Card>
            <CardHeader>
              <CardTitle>تفاصيل العرض</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span>الأولوية:</span>
                <span>{quotation.priority_display}</span>
              </div>
              
              <div className="flex justify-between">
                <span>تاريخ الإنشاء:</span>
                <span>{formatDate(quotation.created_at)}</span>
              </div>
              
              <div className="flex justify-between">
                <span>صالح حتى:</span>
                <span>{formatDate(quotation.valid_until)}</span>
              </div>
              
              {quotation.approved_at && (
                <div className="flex justify-between">
                  <span>تاريخ الموافقة:</span>
                  <span>{formatDate(quotation.approved_at)}</span>
                </div>
              )}
              
              <div className="flex justify-between">
                <span>مندوب المبيعات:</span>
                <span>{quotation.sales_rep.first_name} {quotation.sales_rep.last_name}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default QuotationDetailPage;
