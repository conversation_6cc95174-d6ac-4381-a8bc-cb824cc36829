import axios from 'axios';

// API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000,
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('access_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      if (typeof window !== 'undefined') {
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

// Types
export interface ServiceCatalog {
  id: number;
  name_ar: string;
  name_en: string;
  category: string;
  category_display: string;
  base_price: string;
  unit: string;
  unit_display: string;
  description_ar: string;
  description_en?: string;
  estimated_hours_min: number;
  estimated_hours_max: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface QuotationItem {
  id: number;
  service: ServiceCatalog;
  service_id?: number;
  description: string;
  quantity: number;
  unit_price: string;
  total_price: string;
  complexity_level: string;
  complexity_level_display: string;
  estimated_hours: number;
  created_at: string;
}

export interface Quotation {
  id: number;
  quotation_number: string;
  title: string;
  description?: string;
  client: {
    id: number;
    name: string;
    email: string;
    company?: string;
  };
  sales_rep: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
  };
  status: string;
  status_display: string;
  priority: string;
  priority_display: string;
  subtotal: string;
  discount_percentage: string;
  discount_amount: string;
  tax_percentage: string;
  tax_amount: string;
  total_amount: string;
  valid_until: string;
  approved_at?: string;
  approved_by?: any;
  converted_to_project?: any;
  notes?: string;
  terms_conditions?: string;
  created_at: string;
  updated_at: string;
  is_expired: boolean;
  days_until_expiry: number;
  items: QuotationItem[];
  items_count: number;
}

export interface QuotationStats {
  total_quotations: number;
  pending_quotations: number;
  approved_quotations: number;
  rejected_quotations: number;
  expired_quotations: number;
  converted_quotations: number;
  total_value: string;
  approved_value: string;
  conversion_rate: string;
  average_quotation_value: string;
}

export interface CreateQuotationData {
  title: string;
  description?: string;
  client_id: number;
  priority?: string;
  discount_percentage?: number;
  tax_percentage?: number;
  valid_until?: string;
  notes?: string;
  terms_conditions?: string;
  items_data?: {
    service: number;
    description: string;
    quantity: number;
    unit_price: number;
    complexity_level?: string;
    estimated_hours?: number;
  }[];
}

// Service Catalog API
export const serviceCatalogAPI = {
  getServices: async (params?: any) => {
    const response = await api.get('/service-catalog/', { params });
    return response.data;
  },

  getService: async (id: number) => {
    const response = await api.get(`/service-catalog/${id}/`);
    return response.data;
  },

  createService: async (data: Partial<ServiceCatalog>) => {
    const response = await api.post('/service-catalog/', data);
    return response.data;
  },

  updateService: async (id: number, data: Partial<ServiceCatalog>) => {
    const response = await api.patch(`/service-catalog/${id}/`, data);
    return response.data;
  },

  deleteService: async (id: number) => {
    await api.delete(`/service-catalog/${id}/`);
  },

  getCategories: async () => {
    const response = await api.get('/service-catalog/categories/');
    return response.data;
  },

  getServicesByCategory: async (category: string) => {
    const response = await api.get('/service-catalog/by_category/', {
      params: { category }
    });
    return response.data;
  }
};

// Quotations API
export const quotationsAPI = {
  getQuotations: async (params?: any) => {
    const response = await api.get('/quotations/', { params });
    return response.data;
  },

  getQuotation: async (id: number) => {
    const response = await api.get(`/quotations/${id}/`);
    return response.data;
  },

  createQuotation: async (data: CreateQuotationData) => {
    const response = await api.post('/quotations/', data);
    return response.data;
  },

  updateQuotation: async (id: number, data: Partial<Quotation>) => {
    const response = await api.patch(`/quotations/${id}/`, data);
    return response.data;
  },

  deleteQuotation: async (id: number) => {
    await api.delete(`/quotations/${id}/`);
  },

  approveQuotation: async (id: number, action: 'approve' | 'reject', notes?: string) => {
    const response = await api.post(`/quotations/${id}/approve/`, {
      action,
      notes
    });
    return response.data;
  },

  convertToProject: async (id: number) => {
    const response = await api.post(`/quotations/${id}/convert_to_project/`);
    return response.data;
  },

  getStats: async () => {
    const response = await api.get('/quotations/stats/');
    return response.data;
  },

  getExpiringSoon: async (days: number = 7) => {
    const response = await api.get('/quotations/expiring_soon/', {
      params: { days }
    });
    return response.data;
  }
};

// Quotation Items API
export const quotationItemsAPI = {
  getItems: async (params?: any) => {
    const response = await api.get('/quotation-items/', { params });
    return response.data;
  },

  getItem: async (id: number) => {
    const response = await api.get(`/quotation-items/${id}/`);
    return response.data;
  },

  createItem: async (data: Partial<QuotationItem>) => {
    const response = await api.post('/quotation-items/', data);
    return response.data;
  },

  updateItem: async (id: number, data: Partial<QuotationItem>) => {
    const response = await api.patch(`/quotation-items/${id}/`, data);
    return response.data;
  },

  deleteItem: async (id: number) => {
    await api.delete(`/quotation-items/${id}/`);
  }
};
